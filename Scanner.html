<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Standards Scanner</title>
    <link rel="stylesheet" href="Scanner.css?v=2024080503">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔍</text></svg>">
</head>
<body>
    <div class="app-container">
        <header class="app-header">
            <h1 class="app-title">SQL Standards Scanner</h1>
            <p class="app-subtitle">Analyze your SQL code for standards compliance</p>
        </header>

        <main class="split-layout">
            <!-- Left Panel - Input Section -->
            <div class="left-panel">
                <div class="panel-header">
                    <h2 class="panel-title">📝 Input Section</h2>
                    <p class="panel-subtitle">Import/Copy SQL Code</p>
                </div>

                <!-- Action Buttons at Top -->
                <div class="top-controls">
                    <button id="scanBtn" class="scan-button">
                        <span class="button-icon">🔍</span>
                        Scan SQL Code
                    </button>
                    <button id="clearBtn" class="clear-button">
                        <span class="button-icon">🗑️</span>
                        Clear All
                    </button>
                    <button id="exportBtn" class="export-button" style="display: none;">
                        <span class="button-icon">📄</span>
                        Export Report
                    </button>
                    <button id="historyBtn" class="history-button" style="display: none;">
                        <span class="button-icon">📚</span>
                        Review History
                    </button>
                </div>

                <div class="manual-input-section">
                    <div class="input-group">
                        <label for="sqlInput" class="input-label">
                            Paste your SQL Code
                        </label>
                        <div class="sql-input-container">
                            <div class="sql-input-toolbar">
                                <div class="sql-input-info">
                                    <span id="lineCount">Lines: 0</span>
                                    <span id="charCount">Characters: 0</span>
                                    <span id="errorCount" style="display: none;">Errors: 0</span>
                                </div>
                                <div class="sql-input-actions">
                                    <button type="button" class="sql-input-action-btn attach-file-btn" title="Attach SQL file" onclick="sqlScanner.attachFile()">📎</button>
                                    <button type="button" class="sql-input-action-btn" title="Clear content" onclick="sqlScanner.clearSqlInput()">🗑️</button>
                                    <button type="button" class="sql-input-action-btn" title="Format SQL" onclick="sqlScanner.formatSql()">🎨</button>
                                </div>
                            </div>
                                <textarea
                                    id="sqlInput"
                                    class="sql-input"
                                    placeholder="-- STRICT REQUIREMENTS (ALL ERRORS):
-- 1. Mandatory header with 10 fields
-- 2. NO SELECT * allowed
-- 3. NO UPDATE without WHERE clause
-- 4. Input/Output parameters documented
-- 5. Tables used documented

-- Required Header Format:
/*
Name: sp_GetUserData
Objective: Retrieve user data
Created by: John Smith
Created Date: 15-Jan-2024
Modified by: Jane Doe
Modified Date: 20-Jan-2024
Modification purpose: Added filter
Input Parameters: @Status NVARCHAR(20)
Output Parameters: UserId, UserName, Email
Tables Used: UserAccounts, UserProfiles
*/

CREATE PROCEDURE sp_GetUserData
    @Status NVARCHAR(20)
AS
BEGIN
    SELECT UserId, UserName, Email
    FROM UserAccounts
    WHERE Status = @Status
END"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden file input for attach functionality -->
                    <input type="file" id="fileInput" class="file-input" multiple accept=".sql,.txt" style="display: none;" />


            </div>

            <!-- Right Panel - Results Section -->
            <div class="right-panel">
                <div class="panel-header">
                    <h2 class="panel-title">📊 Results Section</h2>
                    <p class="panel-subtitle">Errors/Rejected or Approved</p>
                </div>

                <div class="results-section" id="resultsSection">
                    <div class="results-placeholder" id="resultsPlaceholder">
                        <div class="placeholder-icon">🔍</div>
                        <div class="placeholder-title">Ready to Scan</div>
                        <div class="placeholder-text">Upload files or paste SQL code in the left panel, then click "Scan SQL Code" to analyze your code for standards compliance.</div>
                        <div class="placeholder-features">
                            <div class="feature-item">✅ Mandatory header validation</div>
                            <div class="feature-item">✅ Data type standards</div>
                            <div class="feature-item">✅ SQL best practices</div>
                            <div class="feature-item">✅ Security checks</div>
                            <div class="feature-item">✅ Performance analysis</div>
                        </div>
                    </div>

                    <div class="results-content-wrapper" id="resultsContentWrapper" style="display: none;">
                        <div class="results-header">
                            <h3 class="results-title">Scan Results</h3>
                            <div class="results-summary" id="resultsSummary"></div>
                        </div>

                        <div class="results-content" id="resultsContent">
                            <!-- Results will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- SQL Object Type Selection Modal -->
    <div class="modal-overlay" id="sqlTypeModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">🗂️ Select SQL Object Type</h2>
                <button class="modal-close" id="closeSqlTypeModal">×</button>
            </div>
            <div class="modal-body">
                <div class="sql-type-content">
                    <p class="sql-type-description">Please select the type of SQL object you're analyzing:</p>
                    <div class="sql-type-options">
                        <div class="sql-type-option" data-type="stored-procedure">
                            <div class="sql-type-icon">📋</div>
                            <div class="sql-type-info">
                                <h3 class="sql-type-name">Stored Procedures</h3>
                                <p class="sql-type-desc">CREATE PROCEDURE statements</p>
                            </div>
                        </div>
                        <div class="sql-type-option" data-type="function">
                            <div class="sql-type-icon">⚙️</div>
                            <div class="sql-type-info">
                                <h3 class="sql-type-name">Functions</h3>
                                <p class="sql-type-desc">CREATE FUNCTION statements</p>
                            </div>
                        </div>
                        <div class="sql-type-option" data-type="view">
                            <div class="sql-type-icon">👁️</div>
                            <div class="sql-type-info">
                                <h3 class="sql-type-name">Views</h3>
                                <p class="sql-type-desc">CREATE VIEW statements</p>
                            </div>
                        </div>
                        <div class="sql-type-option" data-type="table">
                            <div class="sql-type-icon">🗃️</div>
                            <div class="sql-type-info">
                                <h3 class="sql-type-name">Tables</h3>
                                <p class="sql-type-desc">CREATE TABLE statements</p>
                            </div>
                        </div>
                    </div>
                    <div class="sql-type-actions">
                        <button id="confirmSqlType" class="confirm-type-button" disabled>
                            <span class="button-icon">✅</span>
                            Confirm Selection
                        </button>
                        <button id="cancelSqlType" class="cancel-type-button">
                            <span class="button-icon">❌</span>
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Review History Modal -->
    <div class="modal-overlay" id="historyModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">📚 Review History</h2>
                <button class="modal-close" id="closeHistoryModal">×</button>
            </div>
            <div class="modal-body">
                <div class="history-filters">
                    <select id="purposeFilter" class="filter-select">
                        <option value="">All Purposes</option>
                    </select>
                    <select id="authorFilter" class="filter-select">
                        <option value="">All Authors</option>
                    </select>
                    <input type="text" id="searchFilter" class="filter-input" placeholder="Search by name or objective...">
                </div>
                <div class="history-content" id="historyContent">
                    <!-- History items will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <script src="Scanner.js"></script>
</body>
</html>